# ATMA API Testing Guide

This guide explains how to test all API endpoints provided by the ATMA backend using the provided testing tools.

## 🔧 Setup

### Prerequisites
- Backend server running (default: http://localhost:3000)
- Test user account with credentials:
  - **Email:** <EMAIL>
  - **Password:** Amiya123

### Test Credentials
The testing scripts use the following dummy credentials:
```
Email: <EMAIL>
Password: Amiya123
```

## 🚀 Testing Methods

### Method 1: Browser-Based Testing (Recommended)

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Open the API testing dashboard:**
   ```bash
   npm run test:api:browser
   ```
   Or manually navigate to: `http://localhost:5173/test-api.html`

3. **Features:**
   - Interactive web interface
   - Real-time test results
   - Visual status indicators
   - Download test results as JSON
   - Individual test execution
   - Complete test suite execution

### Method 2: Node.js Script

1. **Run the complete test suite:**
   ```bash
   npm run test:api
   ```

2. **Features:**
   - Command-line execution
   - Detailed console output
   - Automatic result file generation
   - Sequential test execution with delays

## 📊 API Endpoints Tested

### 🌐 Gateway & Health
- `GET /` - Gateway information
- `GET /health` - Main health check
- `GET /health/live` - Liveness probe
- `GET /health/ready` - Readiness probe
- `GET /health/detailed` - Detailed health information

### 🔐 Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile
- `GET /auth/token-balance` - Get token balance

### 📊 Assessment
- `POST /assessments/submit` - Submit assessment data
- `GET /assessments/status/{jobId}` - Check assessment status

### 📁 Archive
- `GET /archive/results` - Get user results
- `GET /archive/stats` - Get user statistics
- `GET /archive/stats/overview` - Get stats overview

## 🔍 Test Data

### Assessment Data Format
The testing scripts use sample assessment data with the following structure:

```javascript
{
  riasec: {
    realistic: 4.2,
    investigative: 3.8,
    artistic: 4.5,
    social: 3.2,
    enterprising: 3.9,
    conventional: 2.8
  },
  ocean: {
    openness: 4.1,
    conscientiousness: 3.7,
    extraversion: 3.4,
    agreeableness: 4.0,
    neuroticism: 2.5
  },
  viaIs: {
    creativity: 4.3,
    curiosity: 4.1,
    judgment: 3.8,
    // ... (24 character strengths total)
  }
}
```

### Profile Update Data
```javascript
{
  firstName: 'Test',
  lastName: 'User',
  dateOfBirth: '1990-01-01',
  gender: 'other',
  education: 'bachelor',
  experience: '1-3'
}
```

## 📈 Understanding Test Results

### Success Indicators
- ✅ Green checkmarks indicate successful tests
- 📊 Summary shows success/failure counts
- 💾 Results can be downloaded as JSON

### Common Error Scenarios
1. **401 Unauthorized** - Authentication required or token expired
2. **404 Not Found** - Endpoint not available or incorrect URL
3. **500 Internal Server Error** - Backend processing error
4. **Network Error** - Backend server not running

## 🛠️ Troubleshooting

### Backend Not Running
```
❌ Gateway Info: FAILED - Network Error
```
**Solution:** Start the backend server on port 3000

### Authentication Failures
```
❌ Login: FAILED - Invalid credentials
```
**Solution:** Ensure the test user exists in the database

### Token Expired
```
❌ Get Profile: FAILED - Token expired
```
**Solution:** Re-run the login test to get a fresh token

### Assessment Processing
```
ℹ️ Assessment Status: processing
```
**Note:** Assessment processing may take time. The status will update from "processing" to "completed" or "failed"

## 📝 Customization

### Changing API Base URL
**Browser Method:**
1. Use the configuration section in the web interface
2. Update the API Base URL field
3. Click "Update Config"

**Node.js Method:**
1. Set environment variable: `VITE_API_BASE_URL=http://your-api-url`
2. Or modify `src/config/api.js`

### Using Different Credentials
Update the credentials in the test files:
- Browser: Modify `TEST_CREDENTIALS` in `public/test-api.html`
- Node.js: Modify `TEST_CREDENTIALS` in `scripts/api-test.js`

## 📊 Test Results Format

Results are saved in JSON format with the following structure:
```javascript
{
  "endpoint": "/auth/login",
  "method": "POST",
  "success": true,
  "message": "Login successful",
  "data": { /* response data */ },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 🔄 Continuous Testing

For automated testing in CI/CD pipelines, use the Node.js script:
```bash
# In your CI/CD pipeline
npm install
npm run test:api
```

The script will exit with code 0 on success or 1 on failure, making it suitable for automated testing environments.

## 📞 Support

If you encounter issues:
1. Check that the backend server is running
2. Verify the test credentials exist in the database
3. Check the browser console for detailed error messages
4. Review the generated test results JSON file

For additional help, refer to the main project documentation or contact the development team.
